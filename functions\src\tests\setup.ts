/**
 * Configuration globale pour les tests Jest
 * Initialise Firebase Admin SDK pour tous les tests
 */

import * as admin from 'firebase-admin';

// Configuration pour les tests avec émulateurs
const testConfig = {
  projectId: 'sigma-test-project',
  // Utiliser les émulateurs locaux
  databaseURL: 'http://localhost:8080',
  storageBucket: 'sigma-test-project.appspot.com'
};

// Initialiser Firebase Admin une seule fois pour tous les tests
if (!admin.apps.length) {
  admin.initializeApp(testConfig);
  
  // Configurer les émulateurs
  process.env.FIRESTORE_EMULATOR_HOST = 'localhost:8080';
  process.env.FIREBASE_AUTH_EMULATOR_HOST = 'localhost:9099';
  process.env.FIREBASE_STORAGE_EMULATOR_HOST = 'localhost:9199';
  
  console.log('🔧 Firebase Admin SDK initialisé pour les tests');
} else {
  console.log('🔧 Firebase Admin SDK déjà initialisé');
}

// Configuration Jest globale
jest.setTimeout(30000); // 30 secondes timeout pour les tests
