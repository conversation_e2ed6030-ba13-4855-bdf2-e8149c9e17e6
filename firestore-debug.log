Aug 04, 2025 2:05:55 PM com.google.cloud.datastore.emulator.firestore.websocket.WebSocketServer start
INFO: Started WebSocket server on ws://127.0.0.1:9150
API endpoint: http://127.0.0.1:8080
If you are using a library that supports the FIRESTORE_EMULATOR_HOST environment variable, run:

   export FIRESTORE_EMULATOR_HOST=127.0.0.1:8080

If you are running a Firestore in Datastore Mode project, run:

   export DATASTORE_EMULATOR_HOST=127.0.0.1:8080

Note: Support for Datastore Mode is in preview. If you encounter any bugs please file at https://github.com/firebase/firebase-tools/issues.
Dev App Server is now running.

Aug 04, 2025 2:08:59 PM io.gapi.emulators.netty.HttpVersionRoutingHandler channelRead
INFO: Detected non-HTTP/2 connection.
Aug 04, 2025 2:09:00 PM io.gapi.emulators.netty.HttpVersionRoutingHandler channelRead
INFO: Detected HTTP/2 connection.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Aug 04, 2025 2:09:07 PM io.gapi.emulators.netty.HttpVersionRoutingHandler channelRead
INFO: Detected HTTP/2 connection.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Aug 04, 2025 2:09:08 PM io.gapi.emulators.netty.HttpVersionRoutingHandler channelRead
INFO: Detected non-HTTP/2 connection.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Multiple projectIds are not recommended in single project mode. Requested project ID sigma-test-project, but the emulator is configured for sigma-nova. To opt-out of single project mode add/set the '"singleProjectMode": false' property in the firebase.json emulators config.
Aug 04, 2025 2:09:26 PM io.netty.channel.DefaultChannelPipeline onUnhandledInboundException
WARNING: An exceptionCaught() event was fired, and it reached at the tail of the pipeline. It usually means the last handler in the pipeline did not handle the exception.
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:994)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

